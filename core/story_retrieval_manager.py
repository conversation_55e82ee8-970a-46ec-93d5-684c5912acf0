"""
Story Retrieval Manager - Manages long-term memory and story retrieval logic.
Handles sophisticated story filtering, ranking, and relevance scoring for
contextual story selection in conversations.
"""

import logging
from typing import Dict, List, Any
from core.llm_service import llm_service
from core.supabase_client import supabase_client
from core.conversational_state import ConversationalState


logger = logging.getLogger(__name__)


class StoryRetrievalManager:
    """
    Manages story retrieval and long-term memory functionality.
    
    Implements sophisticated story filtering, ranking, and relevance scoring
    to select the most contextually appropriate stories for conversations.
    """

    def __init__(self):
        """Initialize the story retrieval manager."""
        pass

    def filter_stories_by_conversation_state(self, stories: List[Dict[str, Any]], conversation_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Stage 1: Filter stories based on conversation state data from the database.
        Uses triggers, emotions, thoughts, and values from conversation_state table.

        Args:
            stories: List of stories with analysis metadata
            conversation_state: Current conversation state with triggers, emotions, thoughts, values

        Returns:
            Filtered list of stories that match conversation state
        """
        try:
            # Extract conversation state data
            state_triggers = conversation_state.get("triggers", [])
            state_emotions = conversation_state.get("emotions", [])
            state_thoughts = conversation_state.get("thoughts", [])
            state_values = conversation_state.get("values", [])

            filtered_stories = []

            for story in stories:
                story_analysis = story.get("analysis", {})
                if not story_analysis:
                    continue  # Skip stories without analysis

                # Get story analysis data from the new column structure
                story_triggers = story_analysis.get("triggers", [])
                story_emotions = story_analysis.get("emotions", [])
                story_thoughts = story_analysis.get("thoughts", [])
                story_values = story_analysis.get("values", [])

                # Calculate relevance score based on conversation state alignment
                relevance_score = 0.0

                # Check trigger alignment
                if state_triggers and story_triggers:
                    trigger_matches = len(set(state_triggers) & set(story_triggers))
                    relevance_score += trigger_matches * 2.0

                # Check emotional alignment
                if state_emotions and story_emotions:
                    emotion_matches = len(set(state_emotions) & set(story_emotions))
                    relevance_score += emotion_matches * 1.5

                # Check thought pattern alignment
                if state_thoughts and story_thoughts:
                    for state_thought in state_thoughts:
                        for story_thought in story_thoughts:
                            if state_thought.lower() in story_thought.lower() or story_thought.lower() in state_thought.lower():
                                relevance_score += 1.0

                # Check value alignment
                if state_values and story_values:
                    value_matches = len(set(state_values) & set(story_values))
                    relevance_score += value_matches * 2.5  # Higher weight for value alignment

                # Only include stories with some relevance
                if relevance_score > 0.5:
                    story_with_score = {
                        **story,
                        "stage1_relevance_score": relevance_score
                    }
                    filtered_stories.append(story_with_score)

            logger.info(f"Stage 1: Filtered {len(filtered_stories)} stories from {len(stories)} based on conversation state")
            return filtered_stories

        except Exception as e:
            logger.error(f"Error filtering stories by conversation state: {e}")
            return stories  # Return all stories if filtering fails

    def judge_story_relevance_with_llm(self, story: Dict[str, Any], conversation_state: Dict[str, Any], conversation_context: str = "") -> Dict[str, Any]:
        """
        Stage 2: Use a judge LLM to determine story relevance with detailed reasoning.

        Args:
            story: Story data with content and analysis
            conversation_state: Current conversation state data
            conversation_context: Additional context about the conversation

        Returns:
            Dictionary with relevance score, reasoning, and recommendation
        """
        try:
            story_content = story.get("content", "")
            story_title = story.get("title", "Untitled")
            story_analysis = story.get("analysis", {})

            # Extract conversation state data
            state_triggers = conversation_state.get("triggers", [])
            state_emotions = conversation_state.get("emotions", [])
            state_thoughts = conversation_state.get("thoughts", [])
            state_values = conversation_state.get("values", [])
            state_summary = conversation_state.get("summary", "")

            # Extract story analysis data
            story_triggers = story_analysis.get("triggers", [])
            story_emotions = story_analysis.get("emotions", [])
            story_thoughts = story_analysis.get("thoughts", [])
            story_values = story_analysis.get("values", [])

            # Create comprehensive context for judge LLM
            context_description = f"""
            CONVERSATION STATE:
            Summary: {state_summary}
            Current Triggers: {', '.join(state_triggers) if state_triggers else 'None'}
            Current Emotions: {', '.join(state_emotions) if state_emotions else 'None'}
            Current Thoughts: {', '.join(state_thoughts) if state_thoughts else 'None'}
            Current Values: {', '.join(state_values) if state_values else 'None'}

            ADDITIONAL CONTEXT: {conversation_context}
            """

            # Create story summary with analysis
            story_summary = f"""
            STORY: "{story_title}"
            Content: {story_content[:600]}...

            STORY ANALYSIS:
            Triggers: {', '.join(story_triggers) if story_triggers else 'None'}
            Emotions: {', '.join(story_emotions) if story_emotions else 'None'}
            Thoughts: {', '.join(story_thoughts) if story_thoughts else 'None'}
            Values: {', '.join(story_values) if story_values else 'None'}
            """

            # Use structured response for better parsing
            system_prompt = """You are an expert judge for determining story relevance in digital twin conversations.

            Your task is to evaluate whether a story should be shared in the current conversation context.
            Consider psychological alignment, emotional resonance, and conversational appropriateness.

            Provide a detailed assessment with:
            1. Relevance score (0-10)
            2. Reasoning for the score
            3. Recommendation (share/don't_share/discretionary)

            Use 'discretionary' when the story could be relevant but should be left to the LLM's judgment."""

            user_prompt = f"""Evaluate this story for the current conversation:

            {context_description}

            {story_summary}

            Provide your assessment in JSON format."""

            # Define schema for structured response
            schema = {
                "type": "object",
                "properties": {
                    "relevance_score": {
                        "type": "number",
                        "minimum": 0,
                        "maximum": 10,
                        "description": "Relevance score from 0-10"
                    },
                    "reasoning": {
                        "type": "string",
                        "description": "Detailed reasoning for the score"
                    },
                    "recommendation": {
                        "type": "string",
                        "enum": ["share", "don't_share", "discretionary"],
                        "description": "Recommendation for story usage"
                    },
                    "alignment_factors": {
                        "type": "object",
                        "properties": {
                            "emotional_alignment": {"type": "number", "minimum": 0, "maximum": 10},
                            "value_alignment": {"type": "number", "minimum": 0, "maximum": 10},
                            "contextual_relevance": {"type": "number", "minimum": 0, "maximum": 10}
                        },
                        "required": ["emotional_alignment", "value_alignment", "contextual_relevance"]
                    }
                },
                "required": ["relevance_score", "reasoning", "recommendation", "alignment_factors"],
                "additionalProperties": False
            }

            # Get structured response from judge LLM
            response = llm_service.generate_structured_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                schema=schema,
                max_tokens=300
            )

            logger.debug(f"Judge LLM assessment for story {story.get('id')}: {response['relevance_score']:.1f} - {response['recommendation']}")
            return response

        except Exception as e:
            logger.error(f"Error in judge LLM assessment: {e}")
            # Return default assessment
            return {
                "relevance_score": 5.0,
                "reasoning": f"Error in assessment: {str(e)}",
                "recommendation": "discretionary",
                "alignment_factors": {
                    "emotional_alignment": 5.0,
                    "value_alignment": 5.0,
                    "contextual_relevance": 5.0
                }
            }

    def calculate_final_story_score(self, story: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate the final relevance score based on judge assessment and stage 1 filtering.

        Args:
            story: Story with analysis and judge assessment

        Returns:
            Dictionary with detailed scoring breakdown
        """
        try:
            story_id = story.get("id", "unknown")

            # Get scores from different stages
            stage1_score = story.get("stage1_relevance_score", 0.0)
            judge_assessment = story.get("judge_assessment", {})
            judge_score = judge_assessment.get("relevance_score", 0.0)

            # Get alignment factors from judge
            alignment_factors = judge_assessment.get("alignment_factors", {})
            emotional_alignment = alignment_factors.get("emotional_alignment", 5.0)
            value_alignment = alignment_factors.get("value_alignment", 5.0)
            contextual_relevance = alignment_factors.get("contextual_relevance", 5.0)

            # Weighted combination emphasizing judge assessment
            weights = {
                "stage1": 0.2,      # 20% weight for initial filtering
                "judge": 0.8        # 80% weight for judge assessment
            }

            final_score = (weights["stage1"] * stage1_score + weights["judge"] * judge_score)

            return {
                "story_id": story_id,
                "final_score": max(0, final_score),
                "component_scores": {
                    "stage1_score": stage1_score,
                    "judge_score": judge_score,
                    "emotional_alignment": emotional_alignment,
                    "value_alignment": value_alignment,
                    "contextual_relevance": contextual_relevance
                },
                "reasoning": f"Stage1: {stage1_score:.1f}, Judge: {judge_score:.1f}, Recommendation: {judge_assessment.get('recommendation', 'unknown')}"
            }

        except Exception as e:
            logger.error(f"Error calculating final story score: {e}")
            return {
                "story_id": story.get("id", "unknown"),
                "final_score": 0.0,
                "component_scores": {},
                "reasoning": f"Error: {str(e)}"
            }



    def merge_stories_with_analyses(self, stories: List[Any], analyses: List[Any]) -> List[Dict[str, Any]]:
        """
        Merge stories with their corresponding analyses.

        Args:
            stories: List of story records
            analyses: List of story analysis records (from new column structure)

        Returns:
            List of stories with merged analysis data in expected format
        """
        try:
            # Create lookup for analyses by story_id
            analysis_lookup = {}
            for analysis in analyses:
                # Handle both dict and StoryAnalysis objects
                if hasattr(analysis, 'to_dict'):
                    analysis_dict = analysis.to_dict()
                else:
                    analysis_dict = analysis

                story_id = analysis_dict.get("story_id")
                if story_id:
                    # Use the new column-based structure directly
                    analysis_lookup[str(story_id)] = {
                        "triggers": analysis_dict.get("triggers", []),
                        "emotions": analysis_dict.get("emotions", []),
                        "thoughts": analysis_dict.get("thoughts", []),
                        "values": analysis_dict.get("values", [])
                    }

            # Merge stories with analyses
            merged_stories = []
            for story in stories:
                # Handle both dict and Story objects
                if hasattr(story, 'to_dict'):
                    story_dict = story.to_dict()
                else:
                    story_dict = story

                story_id = str(story_dict.get("id"))
                merged_story = {**story_dict}

                if story_id in analysis_lookup:
                    merged_story["analysis"] = analysis_lookup[story_id]

                merged_stories.append(merged_story)

            logger.debug(f"Merged {len(merged_stories)} stories with analyses ({len(analysis_lookup)} analyses available)")
            return merged_stories

        except Exception as e:
            logger.error(f"Error merging stories with analyses: {e}")
            # Convert stories to dict format as fallback
            fallback_stories = []
            for story in stories:
                if hasattr(story, 'to_dict'):
                    fallback_stories.append(story.to_dict())
                else:
                    fallback_stories.append(story)
            return fallback_stories

    def find_relevant_stories(self, user_id: str = "default", conversation_context: str = "", limit: int = 3) -> List[Dict[str, Any]]:
        """
        Find the most relevant stories using enhanced multi-stage filtering with conversation state.

        Args:
            user_id: User ID to get conversation state for
            conversation_context: Additional context about the conversation
            limit: Maximum number of stories to return

        Returns:
            List of relevant stories with comprehensive scoring details, or empty list if none are relevant
        """
        try:
            # Get conversation state from database
            conversation_state_obj = ConversationalState(user_id)
            conversation_state = conversation_state_obj.get_context()

            # Get all stories with their analyses
            stories = supabase_client.get_stories()
            story_analyses = supabase_client.get_story_analyses()

            if not stories:
                logger.warning("No stories found in database")
                return []

            # Merge stories with their analyses
            stories_with_analysis = self.merge_stories_with_analyses(stories, story_analyses)

            logger.info(f"Using enhanced relevance pipeline for {len(stories_with_analysis)} stories")

            # Stage 1: Filter by conversation state alignment
            filtered_stories = self.filter_stories_by_conversation_state(stories_with_analysis, conversation_state["context"])

            if not filtered_stories:
                logger.warning("No stories passed Stage 1 filtering - no relevant stories found")
                return []  # Return empty list if no stories are relevant

            logger.info(f"Stage 1 passed {len(filtered_stories)} stories")

            # Stage 2: Judge LLM assessment for remaining stories
            judge_assessed_stories = []
            for story in filtered_stories:
                judge_result = self.judge_story_relevance_with_llm(story, conversation_state["context"], conversation_context)

                # Only include stories that the judge recommends sharing or marks as discretionary
                if judge_result["recommendation"] in ["share", "discretionary"]:
                    story_with_judge_score = {
                        **story,
                        "judge_assessment": judge_result,
                        "final_relevance_score": judge_result["relevance_score"]
                    }
                    judge_assessed_stories.append(story_with_judge_score)

            if not judge_assessed_stories:
                logger.info("No stories passed Stage 2 judge assessment - no relevant stories to share")
                return []  # Return empty list if judge doesn't recommend any stories

            # Sort by judge relevance score
            judge_assessed_stories.sort(key=lambda x: x["final_relevance_score"], reverse=True)

            # Stage 3: Apply final threshold and limit
            final_stories = []
            for story in judge_assessed_stories:
                if story["final_relevance_score"] >= 6.0:  # Higher threshold for final selection
                    final_stories.append(story)
                if len(final_stories) >= limit:
                    break

            logger.info(f"Final selection: {len(final_stories)} stories from {len(judge_assessed_stories)} judge-approved candidates")

            # Log final selections for debugging
            for i, story in enumerate(final_stories):
                judge_assessment = story["judge_assessment"]
                logger.debug(f"Selected story {i+1}: Score {judge_assessment['relevance_score']:.1f}, "
                           f"Recommendation: {judge_assessment['recommendation']}, "
                           f"Reasoning: {judge_assessment['reasoning'][:100]}...")

            return final_stories

        except Exception as e:
            logger.error(f"Error finding relevant stories: {e}")
            return []

    def get_story_relevance_insights(self, user_id: str = "default", conversation_context: str = "", limit: int = 5) -> Dict[str, Any]:
        """
        Get insights into story relevance scoring for debugging and optimization.

        Args:
            user_id: User ID to get conversation state for
            conversation_context: Additional context about the conversation
            limit: Number of top stories to analyze

        Returns:
            Dictionary containing relevance insights
        """
        try:
            # Get current relevant stories with detailed scoring
            relevant_stories = self.find_relevant_stories(user_id, conversation_context, limit=limit)

            if not relevant_stories:
                return {"status": "no_relevant_stories", "insights": {}}

            insights = {
                "user_id": user_id,
                "conversation_context": conversation_context,
                "top_stories": [],
                "scoring_summary": {
                    "total_candidates": len(relevant_stories),
                    "score_range": {
                        "highest": max(story["final_relevance_score"] for story in relevant_stories),
                        "lowest": min(story["final_relevance_score"] for story in relevant_stories)
                    }
                }
            }

            # Detailed breakdown for top stories
            for i, story in enumerate(relevant_stories[:limit]):
                judge_assessment = story.get("judge_assessment", {})
                story_insight = {
                    "rank": i + 1,
                    "story_id": story.get("id"),
                    "title": story.get("title", "Untitled")[:50],
                    "final_relevance_score": story["final_relevance_score"],
                    "judge_recommendation": judge_assessment.get("recommendation", "unknown"),
                    "judge_reasoning": judge_assessment.get("reasoning", ""),
                    "alignment_factors": judge_assessment.get("alignment_factors", {}),
                    "has_analysis": "analysis" in story
                }
                insights["top_stories"].append(story_insight)

            return {"status": "success", "insights": insights}

        except Exception as e:
            logger.error(f"Error getting story relevance insights: {e}")
            return {"status": "error", "error": str(e)}
